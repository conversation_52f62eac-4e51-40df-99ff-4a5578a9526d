[{"timestamp": "2025-08-18T21:24:11.479239", "suite_performance": {"total_duration": 162.54044818878174, "total_tests": 570, "passed_modules": 63, "failed_modules": 0, "avg_memory_usage": 22.678515400402468, "peak_memory_usage": 22.951923076923077, "avg_cpu_usage": 0.24720570216249713, "peak_cpu_usage": 1.8444444444444446, "parallel_efficiency": 0.9802508537324209, "optimization_suggestions": ["🐌 2 slow tests detected (>10s). Slowest: action11.py (38.2s)", "🚀 Consider using --fast flag for parallel execution to reduce total runtime"]}, "module_metrics": [{"module_name": "action11.py", "duration": 38.20233345031738, "success": true, "test_count": 5, "memory_usage_mb": 22.525098425196852, "cpu_usage_percent": 0.07506561679790026, "start_time": "2025-08-18T21:21:28.937536", "end_time": "2025-08-18T21:22:07.139870", "error_message": null}, {"module_name": "action6_gather.py", "duration": 2.596513032913208, "success": true, "test_count": 7, "memory_usage_mb": 22.620042067307693, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:07.229256", "end_time": "2025-08-18T21:22:09.825769", "error_message": null}, {"module_name": "action7_inbox.py", "duration": 3.3401663303375244, "success": true, "test_count": 9, "memory_usage_mb": 22.663143382352942, "cpu_usage_percent": 0.49411764705882355, "start_time": "2025-08-18T21:22:09.839564", "end_time": "2025-08-18T21:22:13.179731", "error_message": null}, {"module_name": "action8_messaging.py", "duration": 2.725250005722046, "success": true, "test_count": 3, "memory_usage_mb": 22.662946428571427, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:13.253888", "end_time": "2025-08-18T21:22:15.979138", "error_message": null}, {"module_name": "action9_process_productive.py", "duration": 4.546727895736694, "success": true, "test_count": 8, "memory_usage_mb": 22.667289402173914, "cpu_usage_percent": 0.31086956521739134, "start_time": "2025-08-18T21:22:16.066589", "end_time": "2025-08-18T21:22:20.613317", "error_message": null}, {"module_name": "adaptive_rate_limiter.py", "duration": 0.864699125289917, "success": true, "test_count": 4, "memory_usage_mb": 22.66449652777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:20.688593", "end_time": "2025-08-18T21:22:21.553292", "error_message": null}, {"module_name": "ai_interface.py", "duration": 3.3886303901672363, "success": true, "test_count": 10, "memory_usage_mb": 22.670955882352942, "cpu_usage_percent": 0.9147058823529413, "start_time": "2025-08-18T21:22:21.594293", "end_time": "2025-08-18T21:22:24.982923", "error_message": null}, {"module_name": "ai_prompt_utils.py", "duration": 1.8237977027893066, "success": true, "test_count": 23, "memory_usage_mb": 22.67013888888889, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:25.007945", "end_time": "2025-08-18T21:22:26.831743", "error_message": null}, {"module_name": "api_cache.py", "duration": 1.0232508182525635, "success": true, "test_count": 6, "memory_usage_mb": 22.66903409090909, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:26.846247", "end_time": "2025-08-18T21:22:27.869498", "error_message": null}, {"module_name": "api_search_utils.py", "duration": 3.273850440979004, "success": true, "test_count": 6, "memory_usage_mb": 22.632457386363637, "cpu_usage_percent": 0.4303030303030303, "start_time": "2025-08-18T21:22:27.952652", "end_time": "2025-08-18T21:22:31.226502", "error_message": null}, {"module_name": "api_utils.py", "duration": 2.841181993484497, "success": true, "test_count": 18, "memory_usage_mb": 22.62028556034483, "cpu_usage_percent": 0.9827586206896551, "start_time": "2025-08-18T21:22:31.269735", "end_time": "2025-08-18T21:22:34.110917", "error_message": null}, {"module_name": "cache.py", "duration": 1.4076740741729736, "success": true, "test_count": 11, "memory_usage_mb": 22.618861607142858, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:34.183500", "end_time": "2025-08-18T21:22:35.591174", "error_message": null}, {"module_name": "cache_manager.py", "duration": 0.8668317794799805, "success": true, "test_count": 21, "memory_usage_mb": 22.61762152777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:35.592173", "end_time": "2025-08-18T21:22:36.459005", "error_message": null}, {"module_name": "chromedriver.py", "duration": 1.2389049530029297, "success": true, "test_count": 5, "memory_usage_mb": 22.618689903846153, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:36.498085", "end_time": "2025-08-18T21:22:37.736989", "error_message": null}, {"module_name": "config.py", "duration": 0.956500768661499, "success": true, "test_count": 5, "memory_usage_mb": 22.61796875, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:37.804943", "end_time": "2025-08-18T21:22:38.761444", "error_message": null}, {"module_name": "config\\config_manager.py", "duration": 1.259523630142212, "success": true, "test_count": 10, "memory_usage_mb": 22.618689903846153, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:38.810924", "end_time": "2025-08-18T21:22:40.070447", "error_message": null}, {"module_name": "config\\config_schema.py", "duration": 0.861638069152832, "success": true, "test_count": 17, "memory_usage_mb": 22.61762152777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:40.117818", "end_time": "2025-08-18T21:22:40.979456", "error_message": null}, {"module_name": "config\\credential_manager.py", "duration": 0.9646153450012207, "success": true, "test_count": 15, "memory_usage_mb": 22.61796875, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:41.021490", "end_time": "2025-08-18T21:22:41.986105", "error_message": null}, {"module_name": "core\\api_manager.py", "duration": 0.9488546848297119, "success": true, "test_count": 7, "memory_usage_mb": 22.61796875, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:42.026250", "end_time": "2025-08-18T21:22:42.975105", "error_message": null}, {"module_name": "core\\browser_manager.py", "duration": 8.486348390579224, "success": true, "test_count": 11, "memory_usage_mb": 22.620726102941177, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:43.030659", "end_time": "2025-08-18T21:22:51.517008", "error_message": null}, {"module_name": "core\\database_manager.py", "duration": 1.3573479652404785, "success": true, "test_count": 8, "memory_usage_mb": 22.618861607142858, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:51.570670", "end_time": "2025-08-18T21:22:52.928018", "error_message": null}, {"module_name": "core\\dependency_injection.py", "duration": 2.0120904445648193, "success": true, "test_count": 24, "memory_usage_mb": 22.619605654761905, "cpu_usage_percent": 0.680952380952381, "start_time": "2025-08-18T21:22:52.976165", "end_time": "2025-08-18T21:22:54.988255", "error_message": null}, {"module_name": "core\\error_handling.py", "duration": 0.8628420829772949, "success": true, "test_count": 6, "memory_usage_mb": 22.61762152777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:55.086076", "end_time": "2025-08-18T21:22:55.948918", "error_message": null}, {"module_name": "core\\logging_utils.py", "duration": 0.8401727676391602, "success": true, "test_count": 8, "memory_usage_mb": 22.61762152777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:55.991358", "end_time": "2025-08-18T21:22:56.831531", "error_message": null}, {"module_name": "core\\registry_utils.py", "duration": 0.9538867473602295, "success": true, "test_count": 6, "memory_usage_mb": 22.61796875, "cpu_usage_percent": 1.4300000000000002, "start_time": "2025-08-18T21:22:56.898005", "end_time": "2025-08-18T21:22:57.851892", "error_message": null}, {"module_name": "core\\session_cache.py", "duration": 1.0417675971984863, "success": true, "test_count": 9, "memory_usage_mb": 22.61825284090909, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:57.906000", "end_time": "2025-08-18T21:22:58.947768", "error_message": null}, {"module_name": "core\\session_manager.py", "duration": 20.34972882270813, "success": true, "test_count": 15, "memory_usage_mb": 22.62095905172414, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:22:59.012060", "end_time": "2025-08-18T21:23:19.361789", "error_message": null}, {"module_name": "core\\session_validator.py", "duration": 0.9083578586578369, "success": true, "test_count": 9, "memory_usage_mb": 22.61796875, "cpu_usage_percent": 1.6600000000000001, "start_time": "2025-08-18T21:23:19.407596", "end_time": "2025-08-18T21:23:20.315954", "error_message": null}, {"module_name": "core\\system_cache.py", "duration": 2.2582149505615234, "success": true, "test_count": 9, "memory_usage_mb": 22.619735054347824, "cpu_usage_percent": 0.6217391304347827, "start_time": "2025-08-18T21:23:20.411998", "end_time": "2025-08-18T21:23:22.670213", "error_message": null}, {"module_name": "core_imports.py", "duration": 0.8436572551727295, "success": true, "test_count": 12, "memory_usage_mb": 22.61762152777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:22.729783", "end_time": "2025-08-18T21:23:23.573440", "error_message": null}, {"module_name": "credentials.py", "duration": 1.1477487087249756, "success": true, "test_count": 11, "memory_usage_mb": 22.618489583333332, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:23.634623", "end_time": "2025-08-18T21:23:24.782372", "error_message": null}, {"module_name": "database.py", "duration": 1.4566187858581543, "success": true, "test_count": 13, "memory_usage_mb": 22.619270833333335, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:24.841387", "end_time": "2025-08-18T21:23:26.298006", "error_message": null}, {"module_name": "dna_gedcom_crossref.py", "duration": 0.8602073192596436, "success": true, "test_count": 5, "memory_usage_mb": 22.61762152777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:26.350882", "end_time": "2025-08-18T21:23:27.211089", "error_message": null}, {"module_name": "error_handling.py", "duration": 0.8578948974609375, "success": true, "test_count": 20, "memory_usage_mb": 22.61762152777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:27.255365", "end_time": "2025-08-18T21:23:28.113260", "error_message": null}, {"module_name": "extraction_quality.py", "duration": 0.8121116161346436, "success": true, "test_count": 7, "memory_usage_mb": 22.62152777777778, "cpu_usage_percent": 1.588888888888889, "start_time": "2025-08-18T21:23:28.160248", "end_time": "2025-08-18T21:23:28.972360", "error_message": null}, {"module_name": "gedcom_ai_integration.py", "duration": 0.87361741065979, "success": true, "test_count": 5, "memory_usage_mb": 22.62152777777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:29.064083", "end_time": "2025-08-18T21:23:29.937700", "error_message": null}, {"module_name": "gedcom_cache.py", "duration": 1.2233314514160156, "success": true, "test_count": 13, "memory_usage_mb": 22.622596153846153, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:29.968737", "end_time": "2025-08-18T21:23:31.192068", "error_message": null}, {"module_name": "gedcom_intelligence.py", "duration": 0.8452422618865967, "success": true, "test_count": 4, "memory_usage_mb": 22.62152777777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:31.274961", "end_time": "2025-08-18T21:23:32.120203", "error_message": null}, {"module_name": "gedcom_search_utils.py", "duration": 2.810429096221924, "success": true, "test_count": 12, "memory_usage_mb": 22.623883928571427, "cpu_usage_percent": 0.5071428571428571, "start_time": "2025-08-18T21:23:32.179573", "end_time": "2025-08-18T21:23:34.990002", "error_message": null}, {"module_name": "gedcom_utils.py", "duration": 2.4060935974121094, "success": true, "test_count": 6, "memory_usage_mb": 22.623697916666668, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:34.991506", "end_time": "2025-08-18T21:23:37.397600", "error_message": null}, {"module_name": "genealogical_normalization.py", "duration": 0.8107881546020508, "success": true, "test_count": 7, "memory_usage_mb": 22.62152777777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:37.401668", "end_time": "2025-08-18T21:23:38.212456", "error_message": null}, {"module_name": "genealogical_task_templates.py", "duration": 0.9119853973388672, "success": true, "test_count": 17, "memory_usage_mb": 22.621875, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:38.305355", "end_time": "2025-08-18T21:23:39.217340", "error_message": null}, {"module_name": "health_monitor.py", "duration": 0.9263482093811035, "success": true, "test_count": 0, "memory_usage_mb": 22.621875, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:39.309761", "end_time": "2025-08-18T21:23:40.236110", "error_message": null}, {"module_name": "logging_config.py", "duration": 0.9217987060546875, "success": true, "test_count": 3, "memory_usage_mb": 22.64921875, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:40.315061", "end_time": "2025-08-18T21:23:41.236860", "error_message": null}, {"module_name": "memory_optimizer.py", "duration": 0.9763181209564209, "success": true, "test_count": 4, "memory_usage_mb": 22.64921875, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:41.319372", "end_time": "2025-08-18T21:23:42.295691", "error_message": null}, {"module_name": "message_personalization.py", "duration": 0.8916137218475342, "success": true, "test_count": 4, "memory_usage_mb": 22.638020833333332, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:42.324608", "end_time": "2025-08-18T21:23:43.216221", "error_message": null}, {"module_name": "ms_graph_utils.py", "duration": 2.1308491230010986, "success": true, "test_count": 6, "memory_usage_mb": 22.631392045454547, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:43.229814", "end_time": "2025-08-18T21:23:45.360663", "error_message": null}, {"module_name": "my_selectors.py", "duration": 0.8578019142150879, "success": true, "test_count": 10, "memory_usage_mb": 22.62934027777778, "cpu_usage_percent": 1.8444444444444446, "start_time": "2025-08-18T21:23:45.439055", "end_time": "2025-08-18T21:23:46.296857", "error_message": null}, {"module_name": "performance_cache.py", "duration": 0.8985319137573242, "success": true, "test_count": 8, "memory_usage_mb": 22.62934027777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:46.343705", "end_time": "2025-08-18T21:23:47.242237", "error_message": null}, {"module_name": "performance_dashboard.py", "duration": 0.8694784641265869, "success": true, "test_count": 4, "memory_usage_mb": 22.62934027777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:47.248909", "end_time": "2025-08-18T21:23:48.118388", "error_message": null}, {"module_name": "performance_monitor.py", "duration": 5.923473596572876, "success": true, "test_count": 12, "memory_usage_mb": 22.632282838983052, "cpu_usage_percent": 0.2423728813559322, "start_time": "2025-08-18T21:23:48.154668", "end_time": "2025-08-18T21:23:54.078142", "error_message": null}, {"module_name": "performance_orchestrator.py", "duration": 1.062596082687378, "success": true, "test_count": 12, "memory_usage_mb": 22.62997159090909, "cpu_usage_percent": 1.3, "start_time": "2025-08-18T21:23:54.082210", "end_time": "2025-08-18T21:23:55.144806", "error_message": null}, {"module_name": "performance_validation.py", "duration": 2.464122772216797, "success": true, "test_count": 4, "memory_usage_mb": 22.6321875, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:55.189427", "end_time": "2025-08-18T21:23:57.653550", "error_message": null}, {"module_name": "person_search.py", "duration": 1.873624324798584, "success": true, "test_count": 14, "memory_usage_mb": 22.951480263157894, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:23:57.702651", "end_time": "2025-08-18T21:23:59.576275", "error_message": null}, {"module_name": "prompt_telemetry.py", "duration": 0.8672606945037842, "success": true, "test_count": 3, "memory_usage_mb": 22.94965277777778, "cpu_usage_percent": 1.8444444444444446, "start_time": "2025-08-18T21:23:59.610334", "end_time": "2025-08-18T21:24:00.477595", "error_message": null}, {"module_name": "quality_regression_gate.py", "duration": 0.836869478225708, "success": true, "test_count": 2, "memory_usage_mb": 22.94965277777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:24:00.515437", "end_time": "2025-08-18T21:24:01.352307", "error_message": null}, {"module_name": "relationship_utils.py", "duration": 2.5909810066223145, "success": true, "test_count": 10, "memory_usage_mb": 22.951923076923077, "cpu_usage_percent": 0.6461538461538462, "start_time": "2025-08-18T21:24:01.420736", "end_time": "2025-08-18T21:24:04.011717", "error_message": null}, {"module_name": "research_prioritization.py", "duration": 0.8509478569030762, "success": true, "test_count": 4, "memory_usage_mb": 22.94965277777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:24:04.032339", "end_time": "2025-08-18T21:24:04.883287", "error_message": null}, {"module_name": "security_manager.py", "duration": 1.29356050491333, "success": true, "test_count": 11, "memory_usage_mb": 22.950721153846153, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:24:04.937810", "end_time": "2025-08-18T21:24:06.231370", "error_message": null}, {"module_name": "selenium_utils.py", "duration": 1.1551861763000488, "success": true, "test_count": 10, "memory_usage_mb": 22.950520833333332, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:24:06.244918", "end_time": "2025-08-18T21:24:07.400104", "error_message": null}, {"module_name": "standard_imports.py", "duration": 0.8431718349456787, "success": true, "test_count": 9, "memory_usage_mb": 22.94965277777778, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:24:07.454287", "end_time": "2025-08-18T21:24:08.297459", "error_message": null}, {"module_name": "test_framework.py", "duration": 0.9546551704406738, "success": true, "test_count": 3, "memory_usage_mb": 22.95, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:24:08.360411", "end_time": "2025-08-18T21:24:09.315067", "error_message": null}, {"module_name": "utils.py", "duration": 2.0797953605651855, "success": true, "test_count": 10, "memory_usage_mb": 22.951636904761905, "cpu_usage_percent": 0.0, "start_time": "2025-08-18T21:24:09.368676", "end_time": "2025-08-18T21:24:11.448471", "error_message": null}]}]